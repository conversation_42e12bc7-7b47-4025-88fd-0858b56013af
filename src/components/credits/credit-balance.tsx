"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { Coins, Plus, RefreshCw } from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { CreditPurchaseModal } from "./credit-purchase-modal"

interface CreditBalanceProps {
  balance?: number
  isLoading?: boolean
  onRefresh?: () => void
  showPurchaseButton?: boolean
  className?: string
}

export function CreditBalance({ 
  balance, 
  isLoading = false, 
  onRefresh,
  showPurchaseButton = true,
  className 
}: CreditBalanceProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    if (!onRefresh) return
    
    setIsRefreshing(true)
    try {
      await onRefresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh credit balance",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center text-muted-foreground">
            <Coins className="h-5 w-5 mr-2" />
            Sign in to view credits
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Coins className="h-5 w-5 text-yellow-500" />
            Credit Balance
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                  {formatCredits(balance || 0)}
                </Badge>
              )}
              
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </Button>
              )}
            </div>

            {showPurchaseButton && (
              <Button
                onClick={() => setShowPurchaseModal(true)}
                size="sm"
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Buy Credits
              </Button>
            )}
          </div>

          {balance !== undefined && balance < 10 && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                Your credit balance is low. Consider purchasing more credits to continue enjoying premium content.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <CreditPurchaseModal
        open={showPurchaseModal}
        onOpenChange={setShowPurchaseModal}
        onSuccess={() => {
          setShowPurchaseModal(false)
          onRefresh?.()
        }}
      />
    </>
  )
}

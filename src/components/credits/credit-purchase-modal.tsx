"use client"

import React, { useState } from "react"
import { useSession } from "next-auth/react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { Coins, CreditCard, Check, Zap } from "lucide-react"
import { formatCredits, formatCreditPrice } from "@/lib/credits"
import { loadStripe } from "@stripe/stripe-js"

interface CreditPackage {
  id: string
  name: string
  description: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  sortOrder: number
}

interface CreditPurchaseModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreditPurchaseModal({ open, onOpenChange, onSuccess }: CreditPurchaseModalProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [packages, setPackages] = useState<CreditPackage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null)
  const [isPurchasing, setIsPurchasing] = useState(false)

  // Load packages when modal opens
  React.useEffect(() => {
    if (open && packages.length === 0) {
      loadPackages()
    }
  }, [open])

  const loadPackages = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/credits/packages')
      if (!response.ok) throw new Error('Failed to load packages')
      
      const data = await response.json()
      setPackages(data.packages || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load credit packages",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handlePurchase = async (packageId: string) => {
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase credits",
        variant: "destructive"
      })
      return
    }

    setIsPurchasing(true)
    setSelectedPackage(packageId)

    try {
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
      if (!stripe) throw new Error('Stripe not loaded')

      // Create a test payment method (in production, collect from user)
      const { paymentMethod, error: pmError } = await stripe.createPaymentMethod({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2025,
          cvc: '123',
        },
      })

      if (pmError || !paymentMethod) {
        throw new Error(pmError?.message || 'Failed to create payment method')
      }

      // Purchase credits
      const response = await fetch('/api/credits/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          packageId,
          paymentMethodId: paymentMethod.id,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Purchase failed')
      }

      const result = await response.json()

      if (result.requiresAction && result.clientSecret) {
        const { error: confirmError } = await stripe.confirmCardPayment(result.clientSecret)
        if (confirmError) {
          throw new Error(confirmError.message)
        }
      }

      toast({
        title: "Credits Purchased!",
        description: `Successfully purchased ${formatCredits(result.purchase.totalCredits)}`,
      })

      onSuccess?.()
    } catch (error: any) {
      console.error('Purchase error:', error)
      toast({
        title: "Purchase Failed",
        description: error.message || "Failed to purchase credits. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsPurchasing(false)
      setSelectedPackage(null)
    }
  }

  const getPopularBadge = (index: number) => {
    if (index === 1) { // Second package (Value Pack)
      return (
        <Badge className="absolute -top-2 -right-2 bg-blue-500 hover:bg-blue-600">
          Popular
        </Badge>
      )
    }
    return null
  }

  const getBestValueBadge = (index: number) => {
    if (index === packages.length - 1) { // Last package (Ultimate Pack)
      return (
        <Badge className="absolute -top-2 -left-2 bg-green-500 hover:bg-green-600">
          Best Value
        </Badge>
      )
    }
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Purchase Credits
          </DialogTitle>
          <DialogDescription>
            Choose a credit package to unlock premium content. Credits never expire.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          {isLoading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="relative">
                <CardHeader>
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-4 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-4 w-20 mb-4" />
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            ))
          ) : (
            packages.map((pkg, index) => (
              <Card 
                key={pkg.id} 
                className={`relative cursor-pointer transition-all hover:shadow-md ${
                  selectedPackage === pkg.id ? 'ring-2 ring-blue-500' : ''
                }`}
              >
                {getPopularBadge(index)}
                {getBestValueBadge(index)}
                
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{pkg.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {pkg.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        ${pkg.price}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatCredits(pkg.credits)}
                        {pkg.bonusCredits > 0 && (
                          <span className="text-green-600 font-medium">
                            {" "}+ {formatCredits(pkg.bonusCredits)} bonus
                          </span>
                        )}
                      </div>
                    </div>

                    {pkg.bonusCredits > 0 && (
                      <div className="flex items-center justify-center gap-1 text-xs text-green-600">
                        <Zap className="h-3 w-3" />
                        {Math.round((pkg.bonusCredits / pkg.credits) * 100)}% bonus
                      </div>
                    )}

                    <Button
                      onClick={() => handlePurchase(pkg.id)}
                      disabled={isPurchasing}
                      className="w-full"
                      variant={selectedPackage === pkg.id ? "default" : "outline"}
                    >
                      {isPurchasing && selectedPackage === pkg.id ? (
                        <>
                          <CreditCard className="h-4 w-4 mr-2 animate-pulse" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Purchase
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-start gap-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">What you get:</p>
              <ul className="space-y-1">
                <li>• Instant access to premium novels and chapters</li>
                <li>• Credits never expire</li>
                <li>• Support your favorite authors</li>
                <li>• Secure payment processing</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

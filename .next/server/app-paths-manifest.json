{"/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/dashboard/novels/[id]/chapters/[chapterId]/edit/page": "app/dashboard/novels/[id]/chapters/[chapterId]/edit/page.js", "/dashboard/page": "app/dashboard/page.js", "/novels/[id]/chapters/[chapterId]/page": "app/novels/[id]/chapters/[chapterId]/page.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/novels/[id]/chapters/route": "app/api/novels/[id]/chapters/route.js"}